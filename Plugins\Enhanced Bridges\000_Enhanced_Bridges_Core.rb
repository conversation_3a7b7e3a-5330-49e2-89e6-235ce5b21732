#===============================================================================
# Enhanced Bridges Plugin - Core Functionality
#===============================================================================
# This file contains the core helper methods and functionality for the Enhanced
# Bridges plugin.
#===============================================================================

module EnhancedBridges
  # Check if bridges are currently active (on)
  def self.bridges_active?
    return $PokemonGlobal&.bridge && $PokemonGlobal.bridge > 0
  end
  
  # Check if bridges are currently inactive (off)
  def self.bridges_off?
    return !bridges_active?
  end
  
  # Check if a specific coordinate has a bridge tile
  def self.bridge_tile_at?(map, x, y)
    return false if !map || !map.valid?(x, y)
    
    # Check all tile layers for bridge terrain tag
    [2, 1, 0].each do |layer|
      tile_id = map.data[x, y, layer]
      next if tile_id == 0
      terrain = GameData::TerrainTag.try_get(map.terrain_tags[tile_id])
      return true if terrain&.bridge
    end
    return false
  end
  
  # Check if an event is on a bridge tile
  def self.event_on_bridge?(event)
    return false if !event || !event.map
    
    # Check if any tile the event occupies has a bridge terrain tag
    event.each_occupied_tile do |x, y|
      return true if bridge_tile_at?(event.map, x, y)
    end
    return false
  end
  
  # Check if an event should be rendered "above bridge"
  # (event is on bridge tile AND bridges are off)
  def self.event_above_bridge?(event)
    return false if !event
    return bridges_off? && event_on_bridge?(event)
  end
  
  # Check if an event should be non-triggerable due to bridge state
  # (event is above bridge)
  def self.event_non_triggerable?(event)
    return event_above_bridge?(event)
  end
  
  # Check if an event should be passable due to bridge state
  # (event is above bridge)
  def self.event_passable_due_to_bridge?(event)
    return event_above_bridge?(event)
  end
  
  # Get the z-coordinate adjustment for events above bridges
  def self.bridge_z_adjustment
    return 64  # Render events 64 pixels above normal
  end
end

#===============================================================================
# Extensions to Game_Event class for bridge functionality
#===============================================================================
class Game_Event
  # Check if this event is on a bridge tile
  def on_bridge?
    return EnhancedBridges.event_on_bridge?(self)
  end
  
  # Check if this event should be rendered above the bridge
  def above_bridge?
    return EnhancedBridges.event_above_bridge?(self)
  end
  
  # Check if this event should be non-triggerable due to bridge state
  def non_triggerable_due_to_bridge?
    return EnhancedBridges.event_non_triggerable?(self)
  end
  
  # Check if this event should be passable due to bridge state
  def passable_due_to_bridge?
    return EnhancedBridges.event_passable_due_to_bridge?(self)
  end
  
  # Compatibility method for hotfixes
  def on_bridge_when_off?
    return above_bridge?
  end
end

#===============================================================================
# Enhanced bridge control functions
#===============================================================================

# Check if bridges are currently active
def pbBridgeActive?
  return EnhancedBridges.bridges_active?
end

# Enhanced bridge on function with better feedback
def pbBridgeOn(height = 2)
  $PokemonGlobal.bridge = height
  # Refresh all events on the current map to update their rendering
  $game_map&.events&.each_value { |event| event.need_refresh = true }
end

# Enhanced bridge off function with better feedback  
def pbBridgeOff
  $PokemonGlobal.bridge = 0
  # Refresh all events on the current map to update their rendering
  $game_map&.events&.each_value { |event| event.need_refresh = true }
end
