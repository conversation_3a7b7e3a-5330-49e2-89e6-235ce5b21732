#===============================================================================
# Enhanced Bridges Plugin - Collision and Passability Modifications
#===============================================================================
# This file modifies the collision detection system to allow players to pass
# through events that are "above bridges" (on bridge tiles when bridges are off).
#===============================================================================

class Game_Map
  # Override passable? to handle bridge events
  alias enhanced_bridges_passable? passable? unless method_defined?(:enhanced_bridges_passable?)
  
  def passable?(x, y, d, self_event = nil)
    return false if !valid?(x, y)
    bit = (1 << ((d / 2) - 1)) & 0x0f
    
    # Check events for passability, but skip events that are above bridges
    events.each_value do |event|
      next if event.tile_id <= 0
      next if event == self_event
      next if !event.at_coordinate?(x, y)
      next if event.through
      
      # NEW: Skip collision check if event is above bridge (passable due to bridge state)
      next if event.passable_due_to_bridge?
      
      next if GameData::TerrainTag.try_get(@terrain_tags[event.tile_id]).ignore_passability
      passage = @passages[event.tile_id]
      return false if passage & bit != 0
      return false if passage & 0x0f == 0x0f
      return true if @priorities[event.tile_id] == 0
    end
    
    # Continue with normal passability checks
    return playerPassable?(x, y, d, self_event) if self_event == $game_player
    
    # All other events - use original logic
    return enhanced_bridges_passable?(x, y, d, self_event)
  end
  
  # Override passableStrict? to handle bridge events
  alias enhanced_bridges_passableStrict? passableStrict? unless method_defined?(:enhanced_bridges_passableStrict?)
  
  def passableStrict?(x, y, d, self_event = nil)
    return false if !valid?(x, y)
    
    # Check events for strict passability, but skip events that are above bridges
    events.each_value do |event|
      next if event == self_event || event.tile_id < 0 || event.through
      next if !event.at_coordinate?(x, y)
      
      # NEW: Skip collision check if event is above bridge
      next if event.passable_due_to_bridge?
      
      next if GameData::TerrainTag.try_get(@terrain_tags[event.tile_id]).ignore_passability
      return false if @passages[event.tile_id] & 0x0f != 0
      return true if @priorities[event.tile_id] == 0
    end
    
    # Continue with tile-based passability checks
    [2, 1, 0].each do |i|
      tile_id = data[x, y, i]
      next if tile_id == 0
      next if GameData::TerrainTag.try_get(@terrain_tags[tile_id]).ignore_passability
      return false if @passages[tile_id] & 0x0f != 0
      return true if @priorities[tile_id] == 0
    end
    return true
  end
end

#===============================================================================
# Player collision detection modifications
#===============================================================================

class Game_Player
  # Override pbFacingEvent to skip events above bridges
  alias enhanced_bridges_pbFacingEvent pbFacingEvent unless method_defined?(:enhanced_bridges_pbFacingEvent)
  
  def pbFacingEvent
    # Get the event using original method
    event = enhanced_bridges_pbFacingEvent
    
    # If the event is above a bridge, treat it as if there's no event
    return nil if event&.passable_due_to_bridge?
    
    return event
  end
end

#===============================================================================
# Following Pokemon compatibility
#===============================================================================

if defined?(Game_Follower)
  class Game_Follower
    # Override location_passable? to handle bridge events
    alias enhanced_bridges_location_passable? location_passable? unless method_defined?(:enhanced_bridges_location_passable?)
    
    def location_passable?(x, y, direction)
      this_map = self.map
      return false if !this_map || !this_map.valid?(x, y)
      return true if @through
      
      passed_tile_checks = false
      bit = (1 << ((direction / 2) - 1)) & 0x0f
      
      # Check all events for ones using tiles as graphics, and see if they're passable
      this_map.events.each_value do |event|
        next if event.tile_id < 0 || event.through || !event.at_coordinate?(x, y)
        
        # NEW: Skip collision check if event is above bridge
        next if event.passable_due_to_bridge?
        
        tile_data = GameData::TerrainTag.try_get(this_map.terrain_tags[event.tile_id])
        next if tile_data.ignore_passability
        next if tile_data.bridge && $PokemonGlobal.bridge == 0
        return false if tile_data.ledge
        passage = this_map.passages[event.tile_id] || 0
        return false if passage & bit != 0
        passed_tile_checks = true if (tile_data.bridge && $PokemonGlobal.bridge > 0) ||
                                     (this_map.priorities[event.tile_id] || -1) == 0
        break if passed_tile_checks
      end
      
      # Continue with original logic for tile checks
      return enhanced_bridges_location_passable?(x, y, direction) if !passed_tile_checks
      
      # Check all events on the map to see if any are in the way
      this_map.events.each_value do |event|
        next if !event.at_coordinate?(x, y)
        
        # NEW: Skip collision check if event is above bridge
        next if event.passable_due_to_bridge?
        
        return false if !event.through && event.character_name != ""
      end
      
      return true
    end
  end
end
