#===============================================================================
# Enhanced Bridges Plugin - Debug Tools
#===============================================================================
# This file contains debug tools and utilities for testing the Enhanced Bridges
# plugin. These tools are only available in debug mode.
#===============================================================================

if $DEBUG
  #=============================================================================
  # Debug Menu Integration
  #=============================================================================
  
  MenuHandlers.add(:debug_menu, :enhanced_bridges, {
    "name"        => _INTL("Enhanced Bridges"),
    "parent"      => :main,
    "description" => _INTL("Test and debug Enhanced Bridges functionality.")
  })
  
  MenuHandlers.add(:debug_menu, :bridge_toggle, {
    "name"        => _INTL("Toggle Bridge State"),
    "parent"      => :enhanced_bridges,
    "description" => _INTL("Switch bridges on/off."),
    "effect"      => proc {
      if EnhancedBridges.bridges_active?
        pbBridgeOff
        pbMessage(_INTL("Bridges turned OFF. Events on bridges should now render above and be non-triggerable."))
      else
        pbBridgeOn
        pbMessage(_INTL("Bridges turned ON. Events on bridges should now render normally and be triggerable."))
      end
    }
  })
  
  MenuHandlers.add(:debug_menu, :bridge_check_player, {
    "name"        => _INTL("Check Bridge at Player"),
    "parent"      => :enhanced_bridges,
    "description" => _INTL("Check if player is on a bridge tile."),
    "effect"      => proc {
      on_bridge = EnhancedBridges.bridge_tile_at?($game_map, $game_player.x, $game_player.y)
      bridge_state = EnhancedBridges.bridges_active? ? "ON" : "OFF"
      pbMessage(_INTL("Player position: ({1},{2})\nOn bridge tile: {3}\nBridge state: {4}", 
                      $game_player.x, $game_player.y, on_bridge ? "YES" : "NO", bridge_state))
    }
  })
  
  MenuHandlers.add(:debug_menu, :bridge_check_events, {
    "name"        => _INTL("Check Bridge Events"),
    "parent"      => :enhanced_bridges,
    "description" => _INTL("List all events on bridge tiles."),
    "effect"      => proc {
      bridge_events = []
      $game_map.events.each_value do |event|
        if event.on_bridge?
          status = event.above_bridge? ? "ABOVE BRIDGE" : "NORMAL"
          bridge_events << "Event #{event.id} at (#{event.x},#{event.y}) - #{status}"
        end
      end
      
      if bridge_events.empty?
        pbMessage(_INTL("No events found on bridge tiles."))
      else
        message = "Bridge Events:\n" + bridge_events.join("\n")
        pbMessage(_INTL(message))
      end
    }
  })
  
  MenuHandlers.add(:debug_menu, :bridge_test_trainer_sight, {
    "name"        => _INTL("Test Trainer Sight"),
    "parent"      => :enhanced_bridges,
    "description" => _INTL("Test trainer sight mechanics with bridges."),
    "effect"      => proc {
      trainer_events = []
      $game_map.events.each_value do |event|
        if event.name[/(?:sight|trainer)\((\d+)\)/i]
          distance = $~[1].to_i
          on_bridge = event.on_bridge?
          above_bridge = event.above_bridge?
          can_see = pbEventCanReachPlayer?(event, $game_player, distance) && !event.non_triggerable_due_to_bridge?
          
          trainer_events << "Event #{event.id}: Bridge=#{on_bridge}, Above=#{above_bridge}, CanSee=#{can_see}"
        end
      end
      
      if trainer_events.empty?
        pbMessage(_INTL("No trainer events found on current map."))
      else
        message = "Trainer Events:\n" + trainer_events.join("\n")
        pbMessage(_INTL(message))
      end
    }
  })
  
  #=============================================================================
  # Console Commands
  #=============================================================================
  
  # Toggle bridge state
  def bridge_toggle
    if EnhancedBridges.bridges_active?
      pbBridgeOff
      puts "Bridges turned OFF"
    else
      pbBridgeOn
      puts "Bridges turned ON"
    end
  end
  
  # Check current bridge state
  def bridge_state
    state = EnhancedBridges.bridges_active? ? "ON" : "OFF"
    height = $PokemonGlobal&.bridge || 0
    puts "Bridge state: #{state} (height: #{height})"
  end
  
  # Check if player is on bridge
  def bridge_here
    on_bridge = EnhancedBridges.bridge_tile_at?($game_map, $game_player.x, $game_player.y)
    puts "Player at (#{$game_player.x},#{$game_player.y}) - On bridge: #{on_bridge}"
  end
  
  # List all events on bridges
  def bridge_events
    bridge_events = []
    $game_map.events.each_value do |event|
      if event.on_bridge?
        status = event.above_bridge? ? "ABOVE" : "NORMAL"
        bridge_events << "Event #{event.id} at (#{event.x},#{event.y}) - #{status}"
      end
    end
    
    if bridge_events.empty?
      puts "No events on bridge tiles"
    else
      puts "Bridge Events:"
      bridge_events.each { |info| puts "  #{info}" }
    end
  end
  
  # Test event triggering
  def test_bridge_triggering
    puts "Testing event triggering with current bridge state..."
    bridge_state
    
    $game_map.events.each_value do |event|
      next unless event.on_bridge?
      
      triggerable = !event.non_triggerable_due_to_bridge?
      passable = event.passable_due_to_bridge?
      above = event.above_bridge?
      
      puts "Event #{event.id}: Triggerable=#{triggerable}, Passable=#{passable}, Above=#{above}"
    end
  end
  
  # Run comprehensive tests
  def run_bridge_tests
    puts "=== Enhanced Bridges Comprehensive Test ==="
    bridge_state
    bridge_here
    bridge_events
    test_bridge_triggering
    puts "=== Test Complete ==="
  end
  
  # Quick functionality demo
  def demo_bridges
    puts "=== Enhanced Bridges Demo ==="
    puts "Starting with bridges ON..."
    pbBridgeOn
    bridge_state
    bridge_events
    
    puts "\nSwitching to bridges OFF..."
    pbBridgeOff
    bridge_state
    bridge_events
    
    puts "\nDemo complete. Bridge state is now OFF."
    puts "Events on bridges should be above bridge, non-triggerable, and passable."
  end
end
