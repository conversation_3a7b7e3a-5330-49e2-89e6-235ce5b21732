#===============================================================================
# Enhanced Bridges Plugin - Rendering Modifications
#===============================================================================
# This file modifies the rendering system to make events on bridge tiles render
# above the bridge when bridges are off.
#===============================================================================

class Game_Event
  # Override screen_z to render events above bridges when appropriate
  alias enhanced_bridges_screen_z screen_z unless method_defined?(:enhanced_bridges_screen_z)
  
  def screen_z(height = 0)
    # Get the normal z coordinate
    normal_z = enhanced_bridges_screen_z(height)
    
    # If this event should be rendered above the bridge, add the adjustment
    if above_bridge?
      return normal_z + EnhancedBridges.bridge_z_adjustment
    end
    
    return normal_z
  end
end

#===============================================================================
# Following Pokemon compatibility
#===============================================================================
# Ensure following Pokemon also render correctly with bridges

if defined?(FollowingPkmn)
  class Game_FollowerEvent
    # Override screen_z for following Pokemon to handle bridges
    alias enhanced_bridges_screen_z screen_z unless method_defined?(:enhanced_bridges_screen_z)
    
    def screen_z(height = 0)
      # Get the normal z coordinate
      normal_z = enhanced_bridges_screen_z(height)
      
      # If this follower should be rendered above the bridge, add the adjustment
      if above_bridge?
        return normal_z + EnhancedBridges.bridge_z_adjustment
      end
      
      return normal_z
    end
  end
end
