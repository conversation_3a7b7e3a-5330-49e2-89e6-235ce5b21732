#===============================================================================
# Enhanced Bridges Plugin - Event Triggering Modifications
#===============================================================================
# This file modifies all event triggering mechanisms to skip events that are
# "above bridges" (on bridge tiles when bridges are off).
#===============================================================================

#===============================================================================
# Game_Event triggering modifications
#===============================================================================

class Game_Event
  # Override check_event_trigger_touch to skip when above bridge
  alias enhanced_bridges_check_event_trigger_touch check_event_trigger_touch unless method_defined?(:enhanced_bridges_check_event_trigger_touch)
  
  def check_event_trigger_touch(dir)
    # Don't trigger if event is above bridge
    return if non_triggerable_due_to_bridge?
    
    enhanced_bridges_check_event_trigger_touch(dir)
  end
  
  # Override check_event_trigger_after_turning to skip when above bridge
  alias enhanced_bridges_check_event_trigger_after_turning check_event_trigger_after_turning unless method_defined?(:enhanced_bridges_check_event_trigger_after_turning)
  
  def check_event_trigger_after_turning
    # Don't trigger if event is above bridge
    return if non_triggerable_due_to_bridge?
    
    enhanced_bridges_check_event_trigger_after_turning
  end
  
  # Override check_event_trigger_after_moving to skip when above bridge
  alias enhanced_bridges_check_event_trigger_after_moving check_event_trigger_after_moving unless method_defined?(:enhanced_bridges_check_event_trigger_after_moving)
  
  def check_event_trigger_after_moving
    # Don't trigger if event is above bridge
    return if non_triggerable_due_to_bridge?
    
    enhanced_bridges_check_event_trigger_after_moving
  end
  
  # Override check_event_trigger_auto to skip when above bridge
  alias enhanced_bridges_check_event_trigger_auto check_event_trigger_auto unless method_defined?(:enhanced_bridges_check_event_trigger_auto)
  
  def check_event_trigger_auto
    # Don't trigger if event is above bridge (except for autorun events)
    if non_triggerable_due_to_bridge? && @trigger != 3  # 3 = Autorun
      return
    end
    
    enhanced_bridges_check_event_trigger_auto
  end
end

#===============================================================================
# Game_Player triggering modifications
#===============================================================================

class Game_Player
  # Override check_event_trigger_here to skip events above bridges
  alias enhanced_bridges_check_event_trigger_here check_event_trigger_here unless method_defined?(:enhanced_bridges_check_event_trigger_here)
  
  def check_event_trigger_here(triggers)
    result = false
    # If event is running
    return result if $game_system.map_interpreter.running?
    
    # All event loops
    $game_map.events.each_value do |event|
      # If event coordinates and triggers are consistent
      next if !event.at_coordinate?(@x, @y)
      next if !triggers.include?(event.trigger)
      
      # NEW: Skip if event is above bridge
      next if event.non_triggerable_due_to_bridge?
      
      # If starting determinant is same position event (other than jumping)
      next if event.jumping? || !event.over_trigger?
      event.start
      result = true if event.starting
    end
    return result
  end
  
  # Override check_event_trigger_there to skip events above bridges
  alias enhanced_bridges_check_event_trigger_there check_event_trigger_there unless method_defined?(:enhanced_bridges_check_event_trigger_there)
  
  def check_event_trigger_there(triggers)
    result = false
    # If event is running
    return result if $game_system.map_interpreter.running?
    
    # Calculate front event coordinates
    new_x = @x + (@direction == 6 ? 1 : @direction == 4 ? -1 : 0)
    new_y = @y + (@direction == 2 ? 1 : @direction == 8 ? -1 : 0)
    return false if !$game_map.valid?(new_x, new_y)
    
    # All event loops
    $game_map.events.each_value do |event|
      next if !triggers.include?(event.trigger)
      # If event coordinates and triggers are consistent
      next if !event.at_coordinate?(new_x, new_y)
      
      # NEW: Skip if event is above bridge
      next if event.non_triggerable_due_to_bridge?
      
      # If starting determinant is front event (other than jumping)
      next if event.jumping? || event.over_trigger?
      event.start
      result = true if event.starting
    end
    return result
  end
  
  # Override check_event_trigger_touch to skip events above bridges
  alias enhanced_bridges_check_event_trigger_touch check_event_trigger_touch unless method_defined?(:enhanced_bridges_check_event_trigger_touch)
  
  def check_event_trigger_touch(dir)
    result = false
    return result if $game_system.map_interpreter.running?
    
    # All event loops
    x_offset = (dir == 4) ? -1 : (dir == 6) ? 1 : 0
    y_offset = (dir == 8) ? -1 : (dir == 2) ? 1 : 0
    $game_map.events.each_value do |event|
      next if ![1, 2].include?(event.trigger)   # Player touch, event touch
      # If event coordinates and triggers are consistent
      next if !event.at_coordinate?(@x + x_offset, @y + y_offset)
      
      # NEW: Skip if event is above bridge
      next if event.non_triggerable_due_to_bridge?
      
      if event.name[/(?:sight|trainer)\((\d+)\)/i]
        distance = $~[1].to_i
        next if !pbEventCanReachPlayer?(event, self, distance)
      elsif event.name[/counter\((\d+)\)/i]
        distance = $~[1].to_i
        next if !pbEventFacesPlayer?(event, self, distance)
      end
      next if event.jumping? || event.over_trigger?
      event.start
      result = true if event.starting
    end
    return result
  end
end
