# Enhanced Bridges Plugin

A comprehensive bridge system plugin for Pokemon Essentials that overcomes the limitations of the 2D engine and makes bridges work properly.

## Features

### Core Functionality
- **Proper Bridge State Management**: Enhanced `pbBridgeOn` and `pbBridgeOff` functions
- **Event Rendering Above Bridges**: Events on bridge tiles render above the bridge when bridges are off
- **Non-Triggerable Bridge Events**: Events on bridges cannot be triggered when bridges are off
- **Passable Bridge Events**: Players can walk through events on bridges when bridges are off
- **Player Movement Under Bridges**: Players can move under bridges when `pbBridgeOff` is active

### Advanced Features
- **Trainer Sight Blocking**: Trainers on bridges cannot spot players when bridges are off
- **Comprehensive Event Coverage**: Handles all event triggering mechanisms
- **Multi-Map Support**: Works across map transfers and connections
- **Debug Tools**: Extensive testing and debugging utilities (debug mode only)

## Installation

1. Copy the `Enhanced Bridges` folder to your `Plugins` directory
2. The plugin will automatically load when you start your game
3. No additional configuration required

## Usage

### Basic Bridge Control

```ruby
# Turn bridges on (default height of 2)
pbBridgeOn

# Turn bridges on with custom height
pbBridgeOn(3)

# Turn bridges off
pbBridgeOff

# Check if bridges are active
if pbBridgeActive?
  # Bridges are on
else
  # Bridges are off
end
```

### Map Setup

1. **Create Bridge Tiles**: Use terrain tag 15 (Bridge) for your bridge tiles
2. **Place Events**: Place events (trainers, NPCs, items) on bridge tiles as normal
3. **Test Functionality**: Use `pbBridgeOff` to test that events render above and become non-triggerable

### Event Behavior

When bridges are **OFF** (`pbBridgeOff`):
- Events on bridge tiles render **above** the bridge
- Events on bridge tiles are **non-triggerable** (cannot be activated)
- Events on bridge tiles are **passable** (player walks through them)
- Trainers on bridge tiles **cannot spot** the player
- Player can move **under** the bridge

When bridges are **ON** (`pbBridgeOn`):
- Events on bridge tiles render **normally**
- Events on bridge tiles are **triggerable** (can be activated)
- Events on bridge tiles have **normal collision** (player cannot pass through)
- Trainers on bridge tiles **can spot** the player normally
- Player moves **on top** of the bridge

## Debug Tools (Debug Mode Only)

### Debug Menu
Access via Debug Menu → Enhanced Bridges:
- **Toggle Bridge State**: Switch bridges on/off
- **Check Bridge at Player**: See if player is on a bridge tile
- **Check Bridge Events**: List all events on bridge tiles
- **Test Trainer Sight**: Test trainer sight mechanics with bridges

### Console Commands
```ruby
# Toggle bridge state
bridge_toggle

# Check current bridge state
bridge_state

# Check if player is on bridge
bridge_here

# List all events on bridges
bridge_events

# Test event triggering
test_bridge_triggering

# Run comprehensive tests
run_bridge_tests

# Quick functionality demo
demo_bridges
```

## Technical Details

### Bridge Detection
The plugin automatically detects bridge tiles using terrain tag 15 (Bridge). Events are considered "on a bridge" if any tile layer at their position has the bridge terrain tag.

### Rendering System
Events on bridges when bridges are off are rendered with a higher z-coordinate to appear above the bridge. The z-coordinate is calculated as:
```
bridge_tile_z + 64
```

### Collision System
The plugin modifies the collision detection system to allow players to pass through events that are "above bridges" (on bridge tiles when bridges are off).

### Event Triggering
All event triggering mechanisms are enhanced to skip events that are "above bridges":
- Player touch events
- Event touch events
- Action button events
- Trainer sight events
- Auto-run events

## Compatibility

- **Pokemon Essentials**: v21.1+
- **Other Plugins**: Compatible with most plugins
- **Following Pokemon**: Fully compatible (followers also move under bridges)
- **Custom Events**: Works with custom event types that inherit from Game_Event

## Troubleshooting

### Events Not Rendering Above Bridge
- Ensure the tile has terrain tag 15 (Bridge)
- Check that `pbBridgeOff` has been called
- Verify the event is actually on the bridge tile coordinates

### Events Still Triggerable on Bridge
- Make sure the event is on a bridge tile (terrain tag 15)
- Confirm bridges are off (`pbBridgeOff`)
- Check for custom event triggering code that might bypass the plugin

### Player Cannot Pass Through Bridge Events
- Verify the event is on a bridge tile
- Ensure bridges are off
- Check that the event has a graphic (events without graphics behave differently)

## Support

This plugin was created by Augment Agent for Pokemon Essentials v21.1+. For issues or questions, please check the troubleshooting section above or use the debug tools to diagnose problems.
